import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { Chatbot, UploadedFile } from '../types';
import { generateId } from '../lib/utils';

interface BotState {
  bots: Chatbot[];
  isLoading: boolean;
  createBot: (title: string, description: string, ownerId: string, icon?: string, files?: File[]) => Promise<Chatbot>;
  updateBot: (id: string, updates: Partial<Chatbot>) => Promise<void>;
  deleteBot: (id: string) => Promise<void>;
  getBot: (id: string) => Chatbot | undefined;
  getUserBots: (userId: string) => Chatbot[];
  uploadFiles: (botId: string, files: File[]) => Promise<UploadedFile[]>;
  downloadBot: (botId: string) => Promise<void>;
  clearUserData: () => void;
}

export const useBotStore = create<BotState>()(
  persist(
    (set, get) => ({
      bots: [],
      isLoading: false,

      createBot: async (title, description, ownerId, icon, files = []) => {
        set({ isLoading: true });
        try {
          // Simulate API call
          await new Promise((resolve) => setTimeout(resolve, 1000));

          const uploadedFiles: UploadedFile[] = files.map(file => ({
            id: generateId(),
            name: file.name,
            type: file.type,
            size: file.size,
            url: URL.createObjectURL(file),
            uploadedAt: new Date().toISOString(),
          }));

          const newBot: Chatbot = {
            id: generateId(),
            title,
            description,
            icon,
            ownerId, // Use the actual user ID
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            files: uploadedFiles,
          };

          set(state => ({ bots: [...state.bots, newBot] }));
          return newBot;
        } catch (error) {
          console.error('Failed to create bot:', error);
          throw error;
        } finally {
          set({ isLoading: false });
        }
      },

      updateBot: async (id, updates) => {
        set({ isLoading: true });
        try {
          // Simulate API call
          await new Promise((resolve) => setTimeout(resolve, 1000));

          set(state => ({
            bots: state.bots.map(bot =>
              bot.id === id
                ? { ...bot, ...updates, updatedAt: new Date().toISOString() }
                : bot
            )
          }));
        } catch (error) {
          console.error('Failed to update bot:', error);
          throw error;
        } finally {
          set({ isLoading: false });
        }
      },

      deleteBot: async (id) => {
        set({ isLoading: true });
        try {
          // Simulate API call
          await new Promise((resolve) => setTimeout(resolve, 1000));

          set(state => ({
            bots: state.bots.filter(bot => bot.id !== id)
          }));
        } catch (error) {
          console.error('Failed to delete bot:', error);
          throw error;
        } finally {
          set({ isLoading: false });
        }
      },

      getBot: (id) => {
        return get().bots.find(bot => bot.id === id);
      },

      getUserBots: (userId) => {
        return get().bots.filter(bot => bot.ownerId === userId);
      },

      uploadFiles: async (botId, files) => {
        set({ isLoading: true });
        try {
          // Simulate API call
          await new Promise((resolve) => setTimeout(resolve, 1500));

          const uploadedFiles: UploadedFile[] = files.map(file => ({
            id: generateId(),
            name: file.name,
            type: file.type,
            size: file.size,
            url: URL.createObjectURL(file),
            uploadedAt: new Date().toISOString(),
          }));

          set(state => ({
            bots: state.bots.map(bot =>
              bot.id === botId
                ? {
                    ...bot,
                    files: [...bot.files, ...uploadedFiles],
                    updatedAt: new Date().toISOString()
                  }
                : bot
            )
          }));

          return uploadedFiles;
        } catch (error) {
          console.error('Failed to upload files:', error);
          throw error;
        } finally {
          set({ isLoading: false });
        }
      },

      downloadBot: async (botId) => {
        try {
          const bot = get().bots.find(b => b.id === botId);
          if (!bot) {
            throw new Error('Bot not found');
          }

          // Create bot data for download
          const botData = {
            id: bot.id,
            title: bot.title,
            description: bot.description,
            icon: bot.icon,
            createdAt: bot.createdAt,
            updatedAt: bot.updatedAt,
            files: bot.files.map(file => ({
              id: file.id,
              name: file.name,
              type: file.type,
              size: file.size,
              uploadedAt: file.uploadedAt
            })),
            exportedAt: new Date().toISOString(),
            version: '1.0'
          };

          // Create and download JSON file
          const dataStr = JSON.stringify(botData, null, 2);
          const dataBlob = new Blob([dataStr], { type: 'application/json' });
          const url = URL.createObjectURL(dataBlob);

          const link = document.createElement('a');
          link.href = url;
          link.download = `${bot.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_bot_export.json`;
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          // Clean up the URL
          URL.revokeObjectURL(url);
        } catch (error) {
          console.error('Failed to download bot:', error);
          throw error;
        }
      },

      clearUserData: () => {
        set({ bots: [] });
      },
    }),
    {
      name: 'docuchat-bots',
      storage: createJSONStorage(() => localStorage),
    }
  )
);