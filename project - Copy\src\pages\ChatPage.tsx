import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowLeft, FileText, Trash2, Settings, Share } from 'lucide-react';
import { useBotStore } from '../store/botStore';
import ChatInterface from '../components/ChatInterface';
import Button from '../components/ui/Button';
import FileUpload from '../components/ui/FileUpload';
import Navbar from '../components/layout/Navbar';

const ChatPage: React.FC = () => {
  const { botId } = useParams<{ botId: string }>();
  const { getBot, uploadFiles, isLoading } = useBotStore();
  const [bot, setBot] = useState(botId ? getBot(botId) : undefined);
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  
  useEffect(() => {
    if (botId) {
      setBot(getBot(botId));
    }
  }, [botId, getBot]);
  
  const handleFileUpload = async (files: File[]) => {
    if (botId && files.length > 0) {
      try {
        await uploadFiles(botId, files);
        setShowFileUpload(false);
        // Refresh bot data
        setBot(getBot(botId));
      } catch (error) {
        console.error('Failed to upload files:', error);
      }
    }
  };
  
  if (!bot || !botId) {
    return (
      <div className="min-h-screen bg-navy-900 flex flex-col items-center justify-center">
        <p className="text-white text-xl mb-4">Bot not found</p>
        <Link
          to="/dashboard"
          className="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <ArrowLeft className="h-5 w-5 mr-2" />
          Back to Dashboard
        </Link>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-navy-900 flex flex-col">
      <Navbar />
      
      <div className="flex-1 flex flex-col lg:flex-row">
        {/* Mobile menu toggle */}
        <div className="lg:hidden p-4 bg-navy-800">
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="w-full flex items-center justify-between px-4 py-2 bg-navy-700 rounded-md text-white"
          >
            <span className="font-medium">{isMobileMenuOpen ? 'Hide Files' : 'Show Files'}</span>
            <FileText className="h-5 w-5" />
          </button>
        </div>
        
        {/* Sidebar - File list and actions */}
        <motion.div 
          className={`bg-navy-800 w-full lg:w-80 flex-shrink-0 overflow-y-auto ${isMobileMenuOpen ? 'block' : 'hidden lg:block'}`}
          initial={{ x: -20, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <div className="p-4 border-b border-navy-700">
            <Link to="/dashboard" className="inline-flex items-center text-gray-300 hover:text-white">
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Dashboard
            </Link>
          </div>
          
          <div className="p-4">
            <h2 className="text-lg font-medium text-white mb-4">Bot Details</h2>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-400">Title</p>
                <p className="text-white">{bot.title}</p>
              </div>
              <div>
                <p className="text-sm text-gray-400">Description</p>
                <p className="text-white">{bot.description}</p>
              </div>
            </div>
          </div>
          
          <div className="p-4 border-t border-navy-700">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-medium text-white">Uploaded Files</h2>
              <button 
                onClick={() => setShowFileUpload(!showFileUpload)}
                className="text-pink-400 hover:text-pink-300 text-sm"
              >
                {showFileUpload ? 'Cancel' : 'Add Files'}
              </button>
            </div>
            
            {showFileUpload && (
              <div className="mb-4">
                <FileUpload 
                  onFilesAdded={handleFileUpload}
                  disabled={isLoading}
                  className="border-pink-600/30 hover:border-pink-600/50"
                />
              </div>
            )}
            
            {bot.files.length > 0 ? (
              <ul className="space-y-2">
                {bot.files.map((file) => (
                  <li key={file.id} className="bg-navy-700 rounded-md p-3">
                    <div className="flex items-start">
                      <FileText className="h-5 w-5 text-gray-400 mt-0.5 mr-3 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-white truncate">{file.name}</p>
                        <p className="text-xs text-gray-400">
                          {(file.size / 1024).toFixed(1)} KB
                        </p>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            ) : (
              <p className="text-gray-400 text-sm">No files uploaded yet.</p>
            )}
          </div>
          
          <div className="p-4 border-t border-navy-700">
            <h2 className="text-lg font-medium text-white mb-4">Actions</h2>
            <div className="space-y-2">
              <Button
                variant="outline"
                fullWidth
                className="justify-start border-navy-600 hover:bg-navy-700 text-gray-300"
              >
                <Settings className="h-4 w-4 mr-2" />
                Bot Settings
              </Button>
              <Button
                variant="outline"
                fullWidth
                className="justify-start border-navy-600 hover:bg-navy-700 text-gray-300"
              >
                <Share className="h-4 w-4 mr-2" />
                Share Bot
              </Button>
              <Button
                variant="outline"
                fullWidth
                className="justify-start border-navy-600 hover:bg-navy-700 text-red-400 hover:text-red-300"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Bot
              </Button>
            </div>
          </div>
        </motion.div>
        
        {/* Main chat area */}
        <motion.div 
          className="flex-1 flex flex-col"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <ChatInterface botId={botId} botName={bot.title} />
        </motion.div>
      </div>
    </div>
  );
};

export default ChatPage;