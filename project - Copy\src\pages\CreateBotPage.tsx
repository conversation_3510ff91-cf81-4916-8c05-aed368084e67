import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Upload, Plus, Bot } from 'lucide-react';
import { useBotStore } from '../store/botStore';
import { useAuthStore } from '../store/authStore';
import Navbar from '../components/layout/Navbar';
import Button from '../components/ui/Button';
import Input from '../components/ui/Input';
import FileUpload from '../components/ui/FileUpload';

const createBotSchema = z.object({
  title: z.string().min(3, 'Title must be at least 3 characters').max(50, 'Title must be less than 50 characters'),
  description: z.string().min(10, 'Description must be at least 10 characters').max(500, 'Description must be less than 500 characters'),
});

type CreateBotFormValues = z.infer<typeof createBotSchema>;

const CreateBotPage: React.FC = () => {
  const navigate = useNavigate();
  const { createBot, isLoading } = useBotStore();
  const { user } = useAuthStore();
  const [files, setFiles] = useState<File[]>([]);
  const [botIcon, setBotIcon] = useState<string | null>(null);
  const [iconFile, setIconFile] = useState<File | null>(null);

  const { register, handleSubmit, formState: { errors } } = useForm<CreateBotFormValues>({
    resolver: zodResolver(createBotSchema),
  });

  const handleFileChange = (uploadedFiles: File[]) => {
    setFiles(uploadedFiles);
  };

  const handleIconChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setIconFile(file);

      // Create preview URL
      const reader = new FileReader();
      reader.onload = () => {
        setBotIcon(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = async (data: CreateBotFormValues) => {
    if (!user) {
      console.error('User not authenticated');
      navigate('/login');
      return;
    }

    try {
      const bot = await createBot(data.title, data.description, user.id, botIcon || undefined, files);
      navigate(`/chat/${bot.id}`);
    } catch (error) {
      console.error('Failed to create bot:', error);
    }
  };

  return (
    <div className="min-h-screen bg-navy-900 flex flex-col">
      <Navbar />

      <motion.div
        className="flex-1 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="bg-navy-800 bg-opacity-50 backdrop-blur-sm rounded-xl p-6 md:p-8">
          <h1 className="text-2xl md:text-3xl font-bold text-white mb-6">Create a New Bot</h1>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
            <div className="flex flex-col md:flex-row gap-8">
              <div className="w-full md:w-3/4 space-y-6">
                <div>
                  <label htmlFor="title" className="block text-sm font-medium text-gray-200 mb-1">
                    Bot Title
                  </label>
                  <Input
                    id="title"
                    placeholder="Enter a title for your bot"
                    {...register('title')}
                    error={errors.title?.message}
                    className="bg-navy-700 text-white border-navy-600"
                  />
                </div>

                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-200 mb-1">
                    Bot Description
                  </label>
                  <textarea
                    id="description"
                    placeholder="Enter a description for your bot"
                    {...register('description')}
                    className={`w-full rounded-md border border-navy-600 bg-navy-700 px-3 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent min-h-[120px]
                    ${errors.description ? 'border-red-500 focus:ring-red-500' : ''}`}
                  />
                  {errors.description && (
                    <p className="mt-1 text-sm text-red-500">{errors.description.message}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-200 mb-3">
                    Upload Files to Train Your Bot
                  </label>
                  <FileUpload
                    onFilesAdded={handleFileChange}
                    className="border-pink-600/30 hover:border-pink-600/50"
                  />
                </div>
              </div>

              <div className="w-full md:w-1/4">
                <label className="block text-sm font-medium text-gray-200 mb-3">
                  Bot Icon
                </label>
                <div className="flex flex-col items-center">
                  <div className="w-40 h-40 rounded-full overflow-hidden border-2 border-dashed border-yellow-500/50 flex items-center justify-center bg-navy-700 mb-4">
                    {botIcon ? (
                      <img src={botIcon} alt="Bot icon" className="w-full h-full object-cover" />
                    ) : (
                      <Bot className="h-16 w-16 text-yellow-500/70" />
                    )}
                  </div>

                  <label htmlFor="icon-upload" className="cursor-pointer">
                    <div className="bg-indigo-600 hover:bg-indigo-700 transition-colors text-white px-3 py-2 rounded-md text-sm font-medium flex items-center">
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Icon
                    </div>
                    <input
                      id="icon-upload"
                      type="file"
                      accept="image/*"
                      onChange={handleIconChange}
                      className="hidden"
                    />
                  </label>
                </div>
              </div>
            </div>

            <div className="flex justify-center pt-6">
              <Button
                type="submit"
                variant="primary"
                isLoading={isLoading}
                className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 px-8 py-3 text-lg"
              >
                Create Bot
              </Button>
            </div>
          </form>
        </div>
      </motion.div>

      {/* Wave Animation Background */}
      <div className="absolute bottom-0 left-0 right-0 h-64 z-0 overflow-hidden">
        <div className="absolute bottom-0 left-0 right-0">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" className="w-full h-auto">
            <path fill="rgba(26, 32, 44, 0.4)" fillOpacity="1" d="M0,288L48,272C96,256,192,224,288,213.3C384,203,480,213,576,213.3C672,213,768,203,864,165.3C960,128,1056,64,1152,58.7C1248,53,1344,107,1392,133.3L1440,160L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
          </svg>
        </div>
        <div className="absolute bottom-0 left-0 right-0">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 320" className="w-full h-auto">
            <path fill="rgba(76, 29, 149, 0.15)" fillOpacity="1" d="M0,96L48,128C96,160,192,224,288,240C384,256,480,224,576,197.3C672,171,768,149,864,165.3C960,181,1056,235,1152,234.7C1248,235,1344,181,1392,154.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"></path>
          </svg>
        </div>
      </div>
    </div>
  );
};

export default CreateBotPage;