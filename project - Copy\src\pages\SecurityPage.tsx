import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  Shield, 
  Lock, 
  FileX, 
  UserCheck, 
  Search, 
  Upload, 
  Brain, 
  RotateCcw,
  Key,
  ArrowLeft
} from 'lucide-react';
import { motion } from 'framer-motion';
import Navbar from '../components/layout/Navbar';

const SecurityPage: React.FC = () => {
  const securityMeasures = [
    {
      icon: Lock,
      title: "🔐 End-to-End Encryption",
      description: "All communication between the user and the chatbot is secured using HTTPS (SSL encryption) to protect data during transmission.",
      color: "text-blue-400"
    },
    {
      icon: FileX,
      title: "🧾 No Permanent Storage",
      description: "Uploaded documents and chat history are not stored permanently. Files are automatically deleted after processing or after a short period of inactivity.",
      color: "text-red-400"
    },
    {
      icon: UserCheck,
      title: "🛡️ Access Control",
      description: "Only authorized systems and services can access your data, and no third-party service has access without your permission.",
      color: "text-green-400"
    },
    {
      icon: Search,
      title: "🔍 Input Sanitization",
      description: "All user inputs are sanitized to prevent injection attacks or harmful script execution.",
      color: "text-purple-400"
    },
    {
      icon: Upload,
      title: "🔐 Secure File Uploads",
      description: "File uploads are restricted by type, size, and content to prevent malicious uploads.",
      color: "text-yellow-400"
    },
    {
      icon: Brain,
      title: "🧠 Isolated AI Sessions",
      description: "Each user session is isolated, so your data and chat are never mixed with someone else's.",
      color: "text-orange-400"
    },
    {
      icon: RotateCcw,
      title: "🔄 Regular Security Updates",
      description: "The platform is regularly updated to patch vulnerabilities and improve defenses against threats.",
      color: "text-cyan-400"
    },
    {
      icon: Key,
      title: "🔒 Authentication (Optional)",
      description: "If required, you can enable login or API key authentication to restrict access to trusted users only.",
      color: "text-indigo-400"
    }
  ];

  return (
    <div className="min-h-screen bg-navy-900">
      <Navbar />
      
      <div className="relative overflow-hidden">
        {/* Hero Section */}
        <div className="relative z-10 pt-20 pb-16 px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <div className="flex items-center justify-center mb-6">
                <Shield className="h-16 w-16 text-green-400 mr-4" />
                <h1 className="text-5xl md:text-6xl font-bold text-white">
                  🔒 Security Measures
                </h1>
              </div>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Your security is our priority. Learn about the comprehensive measures we've implemented to protect your data and ensure safe interactions.
              </p>
            </motion.div>

            {/* Back Button */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mb-8"
            >
              <Link
                to="/"
                className="inline-flex items-center text-green-400 hover:text-green-300 transition-colors duration-200"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back to Home
              </Link>
            </motion.div>
          </div>
        </div>

        {/* Security Measures Section */}
        <div className="relative z-10 py-16 px-4">
          <div className="max-w-6xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Comprehensive Security Framework
              </h2>
              <p className="text-lg text-gray-300 max-w-2xl mx-auto">
                We implement multiple layers of security to ensure your documents and conversations remain private and protected.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {securityMeasures.map((measure, index) => {
                const IconComponent = measure.icon;
                return (
                  <motion.div
                    key={index}
                    className="bg-white/10 backdrop-blur-sm rounded-xl p-8 border border-white/20 hover:bg-white/20 hover:border-white/30 transition-all duration-300 group"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
                    whileHover={{ y: -5 }}
                  >
                    <div className="flex items-start mb-4">
                      <IconComponent className={`h-8 w-8 ${measure.color} mr-4 mt-1 group-hover:scale-110 transition-transform duration-300`} />
                      <div>
                        <h3 className="text-xl font-bold text-white group-hover:text-green-300 transition-colors duration-300 mb-3">
                          {measure.title}
                        </h3>
                        <p className="text-gray-300 leading-relaxed">
                          {measure.description}
                        </p>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Security Commitment Section */}
        <div className="relative z-10 py-16 px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.4 }}
              className="bg-gradient-to-r from-green-500/20 to-blue-600/20 backdrop-blur-sm rounded-2xl p-8 border border-green-500/30"
            >
              <div className="text-center">
                <Shield className="h-12 w-12 text-green-400 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-white mb-4">
                  Security-First Approach
                </h3>
                <p className="text-gray-300 mb-6 leading-relaxed">
                  Our platform is built with security as a fundamental principle, not an afterthought. We continuously monitor, 
                  update, and improve our security measures to stay ahead of emerging threats and protect your valuable data.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="bg-white/10 rounded-lg p-4">
                    <Lock className="h-8 w-8 text-blue-400 mx-auto mb-2" />
                    <h4 className="text-white font-semibold mb-1">Encrypted</h4>
                    <p className="text-gray-400 text-sm">All data in transit</p>
                  </div>
                  <div className="bg-white/10 rounded-lg p-4">
                    <FileX className="h-8 w-8 text-red-400 mx-auto mb-2" />
                    <h4 className="text-white font-semibold mb-1">Temporary</h4>
                    <p className="text-gray-400 text-sm">No permanent storage</p>
                  </div>
                  <div className="bg-white/10 rounded-lg p-4">
                    <Brain className="h-8 w-8 text-purple-400 mx-auto mb-2" />
                    <h4 className="text-white font-semibold mb-1">Isolated</h4>
                    <p className="text-gray-400 text-sm">Private sessions</p>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Link
                    to="/privacy"
                    className="inline-flex items-center justify-center px-6 py-3 text-lg font-medium text-white bg-gradient-to-r from-green-500 to-blue-600 rounded-lg hover:from-green-600 hover:to-blue-700 transition-all duration-200"
                  >
                    <Shield className="h-5 w-5 mr-2" />
                    View Privacy Policy
                  </Link>
                  <Link
                    to="/contact"
                    className="inline-flex items-center justify-center px-6 py-3 text-lg font-medium text-green-400 border-2 border-green-400 rounded-lg hover:bg-green-400 hover:text-white transition-all duration-200"
                  >
                    Report Security Issue
                  </Link>
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(8)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 rounded-full bg-green-400/20"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.2, 0.6, 0.2],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default SecurityPage;
