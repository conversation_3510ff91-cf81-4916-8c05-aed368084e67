import React, { useEffect, useRef, useState } from 'react';
import { Send, Bot, Loader } from 'lucide-react';
import { useChatStore } from '../store/chatStore';
import { useAuthStore } from '../store/authStore';
import { Message } from '../types';
import { cn } from '../lib/utils';

interface ChatInterfaceProps {
  botId: string;
  botName: string;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({ botId, botName }) => {
  const { currentSession, createSession, sendMessage, isLoading } = useChatStore();
  const { user } = useAuthStore();
  const [input, setInput] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (user && (!currentSession || currentSession.botId !== botId)) {
      createSession(botId, user.id);
    }
  }, [botId, currentSession, createSession, user]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [currentSession?.messages]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim() && !isLoading) {
      sendMessage(input);
      setInput('');
    }
  };

  const MessageItem: React.FC<{ message: Message }> = ({ message }) => {
    const isUser = message.sender === 'user';

    return (
      <div className={cn(
        'flex w-full mb-4',
        isUser ? 'justify-end' : 'justify-start'
      )}>
        <div className={cn(
          'flex max-w-[80%] rounded-lg p-4',
          isUser
            ? 'bg-indigo-600 text-white rounded-br-none'
            : 'bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 rounded-bl-none'
        )}>
          {!isUser && (
            <Bot className="h-5 w-5 mr-2 text-indigo-500 dark:text-indigo-400 mt-0.5" />
          )}
          <div>
            <p className="text-sm">{message.content}</p>
            <p className="text-xs opacity-70 mt-1 text-right">
              {new Date(message.timestamp).toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit'
              })}
            </p>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="flex flex-col h-full">
      <div className="bg-gray-100 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center space-x-3">
          <div className="h-10 w-10 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center">
            <Bot className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
          </div>
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              {botName}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              Ask me anything about your documents
            </p>
          </div>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {currentSession?.messages && currentSession.messages.length > 0 ? (
          currentSession.messages.map((message) => (
            <MessageItem key={message.id} message={message} />
          ))
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <Bot className="h-16 w-16 text-indigo-300 dark:text-indigo-600 mb-4" />
            <h3 className="text-xl font-medium text-gray-700 dark:text-gray-300">
              Chat with {botName}
            </h3>
            <p className="text-gray-500 dark:text-gray-400 max-w-md mt-2">
              Ask questions about your uploaded documents and I'll find the relevant information for you.
            </p>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>

      <div className="border-t border-gray-200 dark:border-gray-700 p-4">
        <form onSubmit={handleSubmit} className="flex space-x-2">
          <input
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder="Ask a question..."
            className="flex-1 rounded-full border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 px-4 py-2 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:focus:ring-indigo-400"
            disabled={isLoading}
          />
          <button
            type="submit"
            disabled={!input.trim() || isLoading}
            className={cn(
              'rounded-full p-2 focus:outline-none focus:ring-2 focus:ring-indigo-500',
              !input.trim() || isLoading
                ? 'bg-gray-300 dark:bg-gray-700 text-gray-500 dark:text-gray-400 cursor-not-allowed'
                : 'bg-indigo-600 text-white hover:bg-indigo-700'
            )}
          >
            {isLoading ? (
              <Loader className="h-5 w-5 animate-spin" />
            ) : (
              <Send className="h-5 w-5" />
            )}
          </button>
        </form>
      </div>
    </div>
  );
};

export default ChatInterface;