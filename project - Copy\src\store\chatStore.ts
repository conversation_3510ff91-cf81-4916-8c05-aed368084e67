import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import { Message, ChatSession } from '../types';
import { generateId } from '../lib/utils';

interface ChatState {
  sessions: ChatSession[];
  currentSession: ChatSession | null;
  isLoading: boolean;
  createSession: (botId: string, userId: string) => void;
  sendMessage: (content: string) => Promise<void>;
  getSession: (id: string) => ChatSession | undefined;
  getSessions: (botId: string) => ChatSession[];
  getUserSessions: (userId: string) => ChatSession[];
  clearUserData: () => void;
}

export const useChatStore = create<ChatState>()(
  persist(
    (set, get) => ({
      sessions: [],
      currentSession: null,
      isLoading: false,

      createSession: (botId, userId) => {
        const newSession: ChatSession = {
          id: generateId(),
          botId,
          userId,
          messages: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        set(state => ({
          sessions: [...state.sessions, newSession],
          currentSession: newSession,
        }));
      },

      sendMessage: async (content) => {
        const { currentSession } = get();
        if (!currentSession) return;

        // Add user message
        const userMessage: Message = {
          id: generateId(),
          content,
          sender: 'user',
          timestamp: new Date().toISOString(),
          botId: currentSession.botId,
        };

        set(state => ({
          currentSession: state.currentSession
            ? {
                ...state.currentSession,
                messages: [...state.currentSession.messages, userMessage],
                updatedAt: new Date().toISOString(),
              }
            : null,
          sessions: state.sessions.map(session =>
            session.id === currentSession?.id
              ? {
                  ...session,
                  messages: [...session.messages, userMessage],
                  updatedAt: new Date().toISOString(),
                }
              : session
          ),
          isLoading: true,
        }));

        try {
          // Simulate API call to get bot response
          await new Promise(resolve => setTimeout(resolve, 1500));

          // Mock bot response
          const botMessage: Message = {
            id: generateId(),
            content: `This is a simulated response to: "${content}"`,
            sender: 'bot',
            timestamp: new Date().toISOString(),
            botId: currentSession.botId,
          };

          set(state => ({
            currentSession: state.currentSession
              ? {
                  ...state.currentSession,
                  messages: [...state.currentSession.messages, botMessage],
                  updatedAt: new Date().toISOString(),
                }
              : null,
            sessions: state.sessions.map(session =>
              session.id === currentSession?.id
                ? {
                    ...session,
                    messages: [...session.messages, botMessage],
                    updatedAt: new Date().toISOString(),
                  }
                : session
            ),
          }));
        } catch (error) {
          console.error('Failed to get bot response:', error);
        } finally {
          set({ isLoading: false });
        }
      },

      getSession: (id) => {
        return get().sessions.find(session => session.id === id);
      },

      getSessions: (botId) => {
        return get().sessions.filter(session => session.botId === botId);
      },

      getUserSessions: (userId) => {
        return get().sessions.filter(session => session.userId === userId);
      },

      clearUserData: () => {
        set({ sessions: [], currentSession: null });
      },
    }),
    {
      name: 'docuchat-chats',
      storage: createJSONStorage(() => localStorage),
    }
  )
);