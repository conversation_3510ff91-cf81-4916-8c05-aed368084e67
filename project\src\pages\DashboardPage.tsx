import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Plus, Search } from 'lucide-react';
import { useBotStore } from '../store/botStore';
import { useAuthStore } from '../store/authStore';
import { Chatbot } from '../types';
import Navbar from '../components/layout/Navbar';
import Footer from '../components/layout/Footer';
import BotCard from '../components/BotCard';

const DashboardPage: React.FC = () => {
  const { getUserBots, deleteBot, downloadBot } = useBotStore();
  const { user } = useAuthStore();
  const [searchTerm, setSearchTerm] = useState('');
  const [editBot, setEditBot] = useState<Chatbot | null>(null);

  // Get user-specific bots
  const userBots = user ? getUserBots(user.id) : [];

  // Filter bots based on search term
  const filteredBots = userBots.filter(bot =>
    bot.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    bot.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleDeleteBot = (id: string) => {
    if (window.confirm('Are you sure you want to delete this bot? This action cannot be undone.')) {
      deleteBot(id);
    }
  };

  const handleDownloadBot = async (id: string) => {
    try {
      await downloadBot(id);
    } catch (error) {
      console.error('Failed to download bot:', error);
      alert('Failed to download bot. Please try again.');
    }
  };

  const handleEditBot = (bot: Chatbot) => {
    setEditBot(bot);
    // This would typically open a modal, but for simplicity we'll just log it
    console.log('Edit bot:', bot);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100
      }
    }
  };

  return (
    <div className="min-h-screen bg-navy-900 flex flex-col">
      <Navbar />

      <main className="flex-1">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex flex-col md:flex-row md:items-center justify-between mb-8">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h1 className="text-3xl font-bold text-white">Your Chatbots</h1>
              <p className="text-gray-400 mt-1">Create and manage your document-trained chatbots</p>
            </motion.div>

            <motion.div
              className="mt-4 md:mt-0 flex flex-col sm:flex-row gap-4"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search bots..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 w-full sm:w-64 rounded-md bg-navy-800 border border-navy-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                />
              </div>

              <Link
                to="/create-bot"
                className="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                <Plus className="h-5 w-5 mr-2" />
                New Bot
              </Link>
            </motion.div>
          </div>

          {filteredBots.length > 0 ? (
            <motion.div
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6"
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              {filteredBots.map((bot) => (
                <motion.div key={bot.id} variants={itemVariants}>
                  <BotCard
                    bot={bot}
                    onDelete={handleDeleteBot}
                    onEdit={handleEditBot}
                    onDownload={handleDownloadBot}
                  />
                </motion.div>
              ))}
            </motion.div>
          ) : (
            <motion.div
              className="text-center py-12 bg-navy-800 bg-opacity-50 rounded-lg"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              {searchTerm ? (
                <>
                  <h3 className="text-xl font-medium text-white mb-2">No bots match your search</h3>
                  <p className="text-gray-400">Try a different search term or clear the search</p>
                </>
              ) : (
                <>
                  <h3 className="text-xl font-medium text-white mb-2">You don't have any bots yet</h3>
                  <p className="text-gray-400 mb-6">Create your first document-trained chatbot to get started</p>
                  <Link
                    to="/create-bot"
                    className="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    <Plus className="h-5 w-5 mr-2" />
                    Create Your First Bot
                  </Link>
                </>
              )}
            </motion.div>
          )}
        </div>
      </main>

      <Footer />
    </div>
  );
};

export default DashboardPage;