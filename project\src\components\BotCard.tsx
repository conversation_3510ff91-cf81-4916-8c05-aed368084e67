import React from 'react';
import { Link } from 'react-router-dom';
import { Bot, FileText, MoreVertical, Edit, Trash2, Download } from 'lucide-react';
import { Chatbot } from '../types';
import { truncateText, formatDate } from '../lib/utils';

interface BotCardProps {
  bot: Chatbot;
  onDelete?: (id: string) => void;
  onEdit?: (bot: Chatbot) => void;
  onDownload?: (id: string) => void;
}

const BotCard: React.FC<BotCardProps> = ({ bot, onDelete, onEdit, onDownload }) => {
  const [showMenu, setShowMenu] = React.useState(false);
  
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden transition-transform duration-300 hover:scale-105 hover:shadow-lg">
      <div className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 rounded-full bg-indigo-100 dark:bg-indigo-900 flex items-center justify-center">
              {bot.icon ? (
                <img src={bot.icon} alt={bot.title} className="h-8 w-8" />
              ) : (
                <Bot className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
              )}
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                {bot.title}
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {formatDate(bot.createdAt)}
              </p>
            </div>
          </div>
          
          {(onDelete || onEdit) && (
            <div className="relative">
              <button
                onClick={() => setShowMenu(!showMenu)}
                className="p-1 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
              >
                <MoreVertical className="h-5 w-5 text-gray-500 dark:text-gray-400" />
              </button>
              
              {showMenu && (
                <div className="absolute right-0 mt-1 w-48 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-10 ring-1 ring-black ring-opacity-5">
                  {onEdit && (
                    <button
                      onClick={() => {
                        onEdit(bot);
                        setShowMenu(false);
                      }}
                      className="flex w-full items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </button>
                  )}
                  {onDownload && (
                    <button
                      onClick={() => {
                        onDownload(bot.id);
                        setShowMenu(false);
                      }}
                      className="flex w-full items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </button>
                  )}
                  {onDelete && (
                    <button
                      onClick={() => {
                        onDelete(bot.id);
                        setShowMenu(false);
                      }}
                      className="flex w-full items-center px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </button>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
        
        <p className="mt-4 text-sm text-gray-600 dark:text-gray-300">
          {truncateText(bot.description, 120)}
        </p>
        
        <div className="mt-6">
          <div className="flex items-center space-x-2">
            <FileText className="h-4 w-4 text-gray-500 dark:text-gray-400" />
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {bot.files.length} {bot.files.length === 1 ? 'file' : 'files'} uploaded
            </span>
          </div>
        </div>
      </div>
      
      <div className="bg-gray-50 dark:bg-gray-900 px-6 py-4">
        <Link
          to={`/chat/${bot.id}`}
          className="block w-full text-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          Chat with bot
        </Link>
      </div>
    </div>
  );
};

export default BotCard;