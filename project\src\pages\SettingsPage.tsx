import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  Settings, 
  User, 
  Bell, 
  Shield, 
  Palette, 
  Database, 
  Download, 
  Trash2,
  Moon,
  Sun,
  Globe,
  ArrowLeft,
  Save,
  RefreshCw,
  Info
} from 'lucide-react';
import { motion } from 'framer-motion';
import { useAuthStore } from '../store/authStore';
import Navbar from '../components/layout/Navbar';
import Button from '../components/ui/Button';

const SettingsPage: React.FC = () => {
  const { user } = useAuthStore();
  const [darkMode, setDarkMode] = useState(false);
  const [notifications, setNotifications] = useState(true);
  const [language, setLanguage] = useState('en');
  const [autoSave, setAutoSave] = useState(true);

  const settingsSections = [
    {
      icon: User,
      title: "👤 Account Settings",
      description: "Manage your account information and preferences",
      items: [
        { label: "Profile Information", value: "Edit your name, email, and avatar", action: "Edit Profile" },
        { label: "Account Type", value: user?.role === 'admin' ? 'Administrator' : 'Standard User', action: null },
        { label: "Member Since", value: "Recently joined", action: null },
        { label: "Account Status", value: "Active", action: null }
      ],
      color: "text-blue-400"
    },
    {
      icon: Bell,
      title: "🔔 Notification Preferences",
      description: "Control how and when you receive notifications",
      items: [
        { label: "Email Notifications", value: notifications ? "Enabled" : "Disabled", action: "Toggle" },
        { label: "Chat Responses", value: "Instant", action: "Configure" },
        { label: "System Updates", value: "Weekly digest", action: "Configure" },
        { label: "Security Alerts", value: "Always enabled", action: null }
      ],
      color: "text-yellow-400"
    },
    {
      icon: Palette,
      title: "🎨 Appearance & Display",
      description: "Customize the look and feel of your interface",
      items: [
        { label: "Theme", value: darkMode ? "Dark Mode" : "Light Mode", action: "Toggle" },
        { label: "Language", value: "English (US)", action: "Change" },
        { label: "Font Size", value: "Medium", action: "Adjust" },
        { label: "Compact Mode", value: "Disabled", action: "Toggle" }
      ],
      color: "text-purple-400"
    },
    {
      icon: Database,
      title: "💾 Data & Storage",
      description: "Manage your documents, chats, and storage preferences",
      items: [
        { label: "Auto-save Chats", value: autoSave ? "Enabled" : "Disabled", action: "Toggle" },
        { label: "Storage Used", value: "0 MB of unlimited", action: null },
        { label: "Chat History", value: "Last 30 days", action: "Configure" },
        { label: "Document Retention", value: "Session only", action: "Configure" }
      ],
      color: "text-green-400"
    },
    {
      icon: Shield,
      title: "🔒 Privacy & Security",
      description: "Control your privacy settings and security options",
      items: [
        { label: "Two-Factor Authentication", value: "Not configured", action: "Setup" },
        { label: "Session Timeout", value: "24 hours", action: "Configure" },
        { label: "Data Sharing", value: "Disabled", action: null },
        { label: "Activity Log", value: "Enabled", action: "View" }
      ],
      color: "text-red-400"
    },
    {
      icon: Download,
      title: "📥 Import & Export",
      description: "Manage your data import and export preferences",
      items: [
        { label: "Export Chat History", value: "Available", action: "Export" },
        { label: "Export Documents", value: "Available", action: "Export" },
        { label: "Import Settings", value: "From file", action: "Import" },
        { label: "Backup Frequency", value: "Manual", action: "Configure" }
      ],
      color: "text-cyan-400"
    }
  ];

  return (
    <div className="min-h-screen bg-navy-900">
      <Navbar />
      
      <div className="relative overflow-hidden">
        {/* Hero Section */}
        <div className="relative z-10 pt-20 pb-16 px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-12"
            >
              <div className="flex items-center justify-center mb-6">
                <Settings className="h-16 w-16 text-pink-400 mr-4" />
                <h1 className="text-5xl md:text-6xl font-bold text-white">
                  ⚙️ Settings
                </h1>
              </div>
              <p className="text-xl text-gray-300 max-w-3xl mx-auto">
                Customize your Docu Chatbot experience with these comprehensive settings and preferences.
              </p>
            </motion.div>

            {/* Back Button */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="mb-8"
            >
              <Link
                to="/dashboard"
                className="inline-flex items-center text-pink-400 hover:text-pink-300 transition-colors duration-200"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back to Dashboard
              </Link>
            </motion.div>
          </div>
        </div>

        {/* Settings Sections */}
        <div className="relative z-10 py-16 px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {settingsSections.map((section, index) => {
                const IconComponent = section.icon;
                return (
                  <motion.div
                    key={index}
                    className="bg-white/10 backdrop-blur-sm rounded-xl p-8 border border-white/20 hover:bg-white/20 hover:border-white/30 transition-all duration-300"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                  >
                    <div className="flex items-center mb-6">
                      <IconComponent className={`h-8 w-8 ${section.color} mr-4`} />
                      <div>
                        <h3 className="text-xl font-bold text-white mb-2">
                          {section.title}
                        </h3>
                        <p className="text-gray-400 text-sm">
                          {section.description}
                        </p>
                      </div>
                    </div>

                    <div className="space-y-4">
                      {section.items.map((item, itemIndex) => (
                        <div key={itemIndex} className="flex items-center justify-between py-3 border-b border-white/10 last:border-b-0">
                          <div>
                            <p className="text-white font-medium">{item.label}</p>
                            <p className="text-gray-400 text-sm">{item.value}</p>
                          </div>
                          {item.action && (
                            <Button
                              variant="outline"
                              size="sm"
                              className="border-white/20 text-gray-300 hover:bg-white/10"
                            >
                              {item.action}
                            </Button>
                          )}
                        </div>
                      ))}
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Quick Actions Section */}
        <div className="relative z-10 py-16 px-4">
          <div className="max-w-4xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.2 }}
              className="bg-gradient-to-r from-pink-500/20 to-purple-600/20 backdrop-blur-sm rounded-2xl p-8 border border-pink-500/30"
            >
              <div className="text-center mb-8">
                <RefreshCw className="h-12 w-12 text-pink-400 mx-auto mb-4" />
                <h3 className="text-2xl font-bold text-white mb-4">
                  Quick Actions
                </h3>
                <p className="text-gray-300">
                  Common settings and actions you might need
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="bg-white/10 rounded-lg p-6 text-center">
                  <Save className="h-8 w-8 text-green-400 mx-auto mb-3" />
                  <h4 className="text-white font-semibold mb-2">Save All Settings</h4>
                  <p className="text-gray-400 text-sm mb-4">Apply all your changes</p>
                  <Button variant="outline" size="sm" className="border-green-400 text-green-400 hover:bg-green-400 hover:text-white">
                    Save Changes
                  </Button>
                </div>

                <div className="bg-white/10 rounded-lg p-6 text-center">
                  <RefreshCw className="h-8 w-8 text-blue-400 mx-auto mb-3" />
                  <h4 className="text-white font-semibold mb-2">Reset to Default</h4>
                  <p className="text-gray-400 text-sm mb-4">Restore original settings</p>
                  <Button variant="outline" size="sm" className="border-blue-400 text-blue-400 hover:bg-blue-400 hover:text-white">
                    Reset All
                  </Button>
                </div>

                <div className="bg-white/10 rounded-lg p-6 text-center">
                  <Trash2 className="h-8 w-8 text-red-400 mx-auto mb-3" />
                  <h4 className="text-white font-semibold mb-2">Clear All Data</h4>
                  <p className="text-gray-400 text-sm mb-4">Remove all stored data</p>
                  <Button variant="outline" size="sm" className="border-red-400 text-red-400 hover:bg-red-400 hover:text-white">
                    Clear Data
                  </Button>
                </div>
              </div>
            </motion.div>
          </div>
        </div>

        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-2 h-2 rounded-full bg-pink-400/20"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                scale: [1, 1.5, 1],
                opacity: [0.2, 0.6, 0.2],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                delay: Math.random() * 2,
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
